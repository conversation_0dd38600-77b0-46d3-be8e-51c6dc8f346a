pip install crewai

from crewai import Crew, Agent, Task, Process
from crewai.memory import LongTermMemory
from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage
import sqlite3
import uuid

# Setup long-term memory with SQLite
shared_db = "./agent_memorydb.db"
storage = LTMSQLiteStorage(db_path=shared_db)
long_memory = LongTermMemory(storage=storage)

# Simulate session_id for different users
session_id = str(uuid.uuid4())

# Create agent
agent = Agent(
    role="Flight Booking Assistant",
    goal="Manage user's flight booking details like destination and travel date",
    backstory="You remember user's preferences and update them as per instructions.",
    verbose=True,
    memory=True,
    long_term_memory=long_memory,
    user_id=session_id  # Important for session separation
)

# Step 1: Initial destination setting
task1 = Task(
    description="User says: My destination is Pune",
    expected_output="Store user's destination as Pune",
    agent=agent
)

crew1 = Crew(
    agents=[agent],
    tasks=[task1],
    process=Process.sequential,
    memory=True,
    long_term_memory=long_memory
)
crew1.kickoff()

