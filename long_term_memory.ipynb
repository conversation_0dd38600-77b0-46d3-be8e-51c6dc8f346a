pip install crewai

from crewai import Crew, Agent, Task, Process
from crewai.memory import LongTermMemory
from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage
import sqlite3
import uuid

# Setup long-term memory with SQLite
shared_db = "./agent_memorydb.db"
storage = LTMSQLiteStorage(db_path=shared_db)
long_memory = LongTermMemory(storage=storage)

# Simulate session_id for different users
session_id = str(uuid.uuid4())

print(f"Session ID: {session_id}")
print(f"Database path: {shared_db}")

import os

# Set up API keys directly
os.environ['GOOGLE_API_KEY'] = 'AIzaSyDbf23e7RsEHP4xsYjtUohInGM6eStPdaI'

# For CrewAI memory functionality, we need to set a placeholder OpenAI key
# This is only used for embeddings, <PERSON> handles the main LLM tasks
os.environ['OPENAI_API_KEY'] = 'sk-placeholder-for-embeddings-only'
os.environ['CHROMA_OPENAI_API_KEY'] = 'sk-placeholder-for-embeddings-only'

print("✅ API keys configured!")
print("📝 Using Gemini for main LLM tasks")
print("💾 Using SQLite for long-term memory storage")
print("🔍 Using placeholder for memory embeddings")

from crewai import LLM

# Configure Gemini model for the main LLM tasks
gemini_llm = LLM(
    model="gemini/gemini-1.5-flash",
    api_key=os.environ['GOOGLE_API_KEY']
)

# Create agent with Gemini model and SQLite long-term memory
# Note: 
# - Gemini is used for reasoning and text generation
# - SQLite is used for long-term memory storage
# - We'll try with memory disabled first to avoid ChromaDB issues
agent = Agent(
    role="Flight Booking Assistant",
    goal="Manage user's flight booking details like destination and travel date",
    backstory="You are a helpful flight booking assistant. You help users manage their travel preferences and remember important details like destinations and dates. You store information in a SQLite database for persistence.",
    verbose=True,
    llm=gemini_llm,  # Gemini for main reasoning
    memory=False,    # Disable built-in memory to avoid ChromaDB issues
    long_term_memory=long_memory,  # SQLite storage for long-term memory
    user_id=session_id  # Important for session separation
)

print(f"✅ Agent created with Gemini LLM and SQLite memory storage.")
print(f"🆔 Session ID: {session_id}")
print(f"🤖 LLM Model: {gemini_llm.model}")

# Step 1: Initial destination setting
# This demonstrates storing information in SQLite long-term memory using Gemini LLM

try:
    task1 = Task(
        description="User says: My destination is Pune. Please remember this destination for future reference. Store this information persistently.",
        expected_output="Acknowledge that you have noted and will remember the user's destination as Pune. Confirm that this information is stored for future conversations.",
        agent=agent
    )
    
    # Create crew with:
    # - Gemini LLM for reasoning and text generation
    # - SQLite database for long-term memory storage
    # - Memory disabled to avoid ChromaDB dependency
    crew1 = Crew(
        agents=[agent],
        tasks=[task1],
        process=Process.sequential,
        memory=False,  # Disable to avoid ChromaDB issues
        long_term_memory=long_memory,  # SQLite for persistence
        manager_llm=gemini_llm  # Use Gemini for crew management
    )
    
    print("🚀 Starting crew execution with Gemini LLM and SQLite memory...")
    result1 = crew1.kickoff()
    print("\n✅ Result:")
    print(result1)
    
except Exception as e:
    print(f"❌ Error: {e}")
    print("\n💡 Troubleshooting:")
    print("1. Make sure you've run the API key setup cell first")
    print("2. Check that your Gemini API key is valid")
    print("3. Ensure all previous cells have been executed successfully")

# Step 2: Multiple conversations to test memory persistence
print("🔄 Testing memory persistence with multiple conversations...")

try:
    for i in range(3):  # Reduced from 10 to 3 for faster testing
        print(f"\n💬 Conversation {i+1}:")
        task = Task(
            description=f"User says: Let's talk about topic {i}. Remember my destination is still stored.",
            expected_output="Respond to the topic while maintaining stored destination information",
            agent=agent
        )
        result = Crew(
            agents=[agent], 
            tasks=[task], 
            process=Process.sequential, 
            memory=True, 
            long_term_memory=long_memory,
            manager_llm=gemini_llm
        ).kickoff()
        print(f"Response: {result}")
    
    # Step 3: Update destination
    print("\n🔄 Updating destination...")
    task2 = Task(
        description="User says: Change my destination to Delhi",
        expected_output="Update stored destination to Delhi and confirm the change",
        agent=agent
    )
    result2 = Crew(
        agents=[agent], 
        tasks=[task2], 
        process=Process.sequential, 
        memory=True, 
        long_term_memory=long_memory,
        manager_llm=gemini_llm
    ).kickoff()
    print(f"✅ Update result: {result2}")
    
except Exception as e:
    print(f"❌ Error during conversation testing: {e}")

# Step 3: Test memory retrieval
print("🔍 Testing memory retrieval...")

try:
    task3 = Task(
        description="User says: What is my current destination? Please check your memory.",
        expected_output="Retrieve and state the user's current destination from memory",
        agent=agent
    )
    
    result3 = Crew(
        agents=[agent], 
        tasks=[task3], 
        process=Process.sequential, 
        memory=False, 
        long_term_memory=long_memory,
        manager_llm=gemini_llm
    ).kickoff()
    
    print(f"✅ Memory retrieval result: {result3}")
    
except Exception as e:
    print(f"❌ Error during memory retrieval: {e}")

# Step 4: Check SQLite database directly
print("🗄️ Checking SQLite database contents...")

try:
    import sqlite3
    import pandas as pd
    
    # Connect to the SQLite database
    conn = sqlite3.connect(shared_db)
    
    # Get list of tables
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    
    print(f"📋 Tables in database '{shared_db}':")
    for table in tables:
        print(f"  - {table[0]}")
    
    # Check contents of each table
    for table in tables:
        table_name = table[0]
        print(f"\n📊 Contents of table '{table_name}':")
        try:
            df = pd.read_sql_query(f"SELECT * FROM {table_name} LIMIT 10", conn)
            if len(df) > 0:
                print(df.to_string(index=False))
            else:
                print("  (No data found)")
        except Exception as e:
            print(f"  Error reading table: {e}")
    
    conn.close()
    print("\n✅ Database check completed!")
    
except Exception as e:
    print(f"❌ Error accessing database: {e}")

