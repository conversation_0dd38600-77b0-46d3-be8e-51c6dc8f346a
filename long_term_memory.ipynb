pip install crewai

from crewai import Crew, Agent, Task, Process
from crewai.memory import LongTermMemory
from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage
import sqlite3
import uuid

# Setup long-term memory with SQLite
shared_db = "./agent_memorydb.db"
storage = LTMSQLiteStorage(db_path=shared_db)
long_memory = LongTermMemory(storage=storage)

# Simulate session_id for different users
session_id = str(uuid.uuid4())

print(f"Session ID: {session_id}")
print(f"Database path: {shared_db}")

import os
from crewai import LLM

# Configure Gemini model
gemini_llm = LLM(
    model="gemini/gemini-1.5-flash",
    api_key=os.environ['GOOGLE_API_KEY']
)

# Create agent with Gemini model
agent = Agent(
    role="Flight Booking Assistant",
    goal="Manage user's flight booking details like destination and travel date",
    backstory="You remember user's preferences and update them as per instructions.",
    verbose=True,
    llm=gemini_llm,
    memory=True,
    long_term_memory=long_memory,
    user_id=session_id  # Important for session separation
)

import os
from getpass import getpass

# Set up Google Gemini API key
if 'GOOGLE_API_KEY' not in os.environ:
    google_api_key = getpass("Enter your Google API key for Gemini: ")
    os.environ['GOOGLE_API_KEY'] = google_api_key

# For CrewAI memory functionality, we need to disable ChromaDB's OpenAI requirement
# by setting a dummy value or using an alternative approach
os.environ['CHROMA_OPENAI_API_KEY'] = 'dummy_key_for_gemini_usage'

print("Environment variables set successfully!")

# Step 1: Initial destination setting
task1 = Task(
    description="User says: My destination is Pune",
    expected_output="Store user's destination as Pune",
    agent=agent
)

crew1 = Crew(
    agents=[agent],
    tasks=[task1],
    process=Process.sequential,
    memory=True,
    long_term_memory=long_memory,
    manager_llm=gemini_llm  # Use Gemini for crew management
)
crew1.kickoff()

