pip install crewai

from crewai import Crew, Agent, Task, Process
from crewai.memory import LongTermMemory
from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage
import sqlite3
import uuid

# Setup long-term memory with SQLite
shared_db = "./agent_memorydb.db"
storage = LTMSQLiteStorage(db_path=shared_db)
long_memory = LongTermMemory(storage=storage)

# Simulate session_id for different users
session_id = str(uuid.uuid4())

print(f"Session ID: {session_id}")
print(f"Database path: {shared_db}")

import os
from crewai import LLM

# Configure Gemini model for the main LLM tasks
gemini_llm = LLM(
    model="gemini/gemini-1.5-flash",
    api_key=os.environ['GOOGLE_API_KEY']
)

# Create agent with Gemini model and SQLite long-term memory
# Note: 
# - Gemini is used for reasoning and text generation
# - SQLite is used for long-term memory storage
# - OpenAI embeddings are used for memory search/retrieval (required by CrewAI)
agent = Agent(
    role="Flight Booking Assistant",
    goal="Manage user's flight booking details like destination and travel date",
    backstory="You remember user's preferences and update them as per instructions. You use SQLite database to store long-term memories.",
    verbose=True,
    llm=gemini_llm,  # Gemini for main reasoning
    memory=True,     # Enable memory functionality
    long_term_memory=long_memory,  # SQLite storage for long-term memory
    user_id=session_id  # Important for session separation
)

print(f"Agent created with Gemini LLM and SQLite memory storage.")
print(f"Session ID: {session_id}")

import os
from getpass import getpass

# Set up Google Gemini API key
if 'GOOGLE_API_KEY' not in os.environ:
    google_api_key = getpass("Enter your Google API key for Gemini: ")
    os.environ['GOOGLE_API_KEY'] = google_api_key

# CrewAI requires OPENAI_API_KEY for memory functionality even when using SQLite
# We need to provide a valid OpenAI API key for the embedding functionality
# The SQLite storage is for long-term memory, but short-term memory still uses ChromaDB with OpenAI embeddings
if 'OPENAI_API_KEY' not in os.environ:
    openai_api_key = getpass("Enter your OpenAI API key (required for CrewAI memory embeddings): ")
    os.environ['OPENAI_API_KEY'] = openai_api_key
    os.environ['CHROMA_OPENAI_API_KEY'] = openai_api_key
else:
    os.environ['CHROMA_OPENAI_API_KEY'] = os.environ['OPENAI_API_KEY']

print("Environment variables set successfully!")
print("Note: OpenAI API key is used only for memory embeddings, Gemini will be used for the actual LLM tasks.")

# Step 1: Initial destination setting
# This demonstrates storing information in SQLite long-term memory using Gemini LLM
task1 = Task(
    description="User says: My destination is Pune. Store this information in long-term memory.",
    expected_output="Acknowledge that you have stored the user's destination as Pune in your long-term memory database.",
    agent=agent
)

# Create crew with:
# - Gemini LLM for reasoning and text generation
# - SQLite database for long-term memory storage
# - OpenAI embeddings for memory search (required by CrewAI architecture)
crew1 = Crew(
    agents=[agent],
    tasks=[task1],
    process=Process.sequential,
    memory=True,
    long_term_memory=long_memory,
    manager_llm=gemini_llm  # Use Gemini for crew management
)

print("Starting crew execution with Gemini LLM and SQLite memory...")
result1 = crew1.kickoff()
print("\nResult:")
print(result1)

for i in range(10):
    task = Task(
        description=f"User says: Let's talk about topic {i}",
        expected_output="Respond accordingly without affecting stored destination",
        agent=agent
    )
    Crew(agents=[agent], tasks=[task], process=Process.sequential, memory=True, long_term_memory=long_memory).kickoff()

# Step 3: Update destination
task2 = Task(
    description="User says: Change my destination to Delhi",
    expected_output="Update stored destination to Delhi",
    agent=agent
)
Crew(agents=[agent], tasks=[task2], process=Process.sequential, memory=True, long_term_memory=long_memory).kickoff()

task3 = Task(
    description="User says: What is my destination?",
    expected_output="Recall and respond: Your destination is Delhi",
    agent=agent
)
Crew(agents=[agent], tasks=[task3], process=Process.sequential, memory=True, long_term_memory=long_memory).kickoff()

