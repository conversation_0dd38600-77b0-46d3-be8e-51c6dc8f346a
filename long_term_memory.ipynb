pip install crewai

from crewai import Crew, Agent, Task, Process
from crewai.memory import LongTermMemory
from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage
import sqlite3
import uuid

# Setup long-term memory with SQLite
shared_db = "./agent_memorydb.db"
storage = LTMSQLiteStorage(db_path=shared_db)
long_memory = LongTermMemory(storage=storage)

# Simulate session_id for different users
session_id = str(uuid.uuid4())

print(f"Session ID: {session_id}")
print(f"Database path: {shared_db}")

import os

# Set up API keys directly
os.environ['GOOGLE_API_KEY'] = 'AIzaSyB5OjjmOKRANlolBBu5tMT5GbFjKCJzkO4'

# For CrewAI memory functionality, we need to set a placeholder OpenAI key
# This is only used for embeddings, <PERSON> handles the main LLM tasks
os.environ['OPENAI_API_KEY'] = 'sk-placeholder-for-embeddings-only'
os.environ['CHROMA_OPENAI_API_KEY'] = 'sk-placeholder-for-embeddings-only'

print("✅ API keys configured!")
print("📝 Using Gemini for main LLM tasks")
print("💾 Using SQLite for long-term memory storage")
print("🔍 Using placeholder for memory embeddings")

from crewai import LLM

# Configure Gemini model for the main LLM tasks
gemini_llm = LLM(
    model="gemini/gemini-1.5-flash",
    api_key=os.environ['GOOGLE_API_KEY']
)

# Create agent with Gemini model and SQLite long-term memory
# Note: 
# - Gemini is used for reasoning and text generation
# - SQLite is used for long-term memory storage
# - Placeholder key for memory embeddings (CrewAI requirement)
agent = Agent(
    role="Flight Booking Assistant",
    goal="Manage user's flight booking details like destination and travel date",
    backstory="You remember user's preferences and update them as per instructions. You use SQLite database to store long-term memories.",
    verbose=True,
    llm=gemini_llm,  # Gemini for main reasoning
    memory=True,     # Enable memory functionality
    long_term_memory=long_memory,  # SQLite storage for long-term memory
    user_id=session_id  # Important for session separation
)

print(f"✅ Agent created with Gemini LLM and SQLite memory storage.")
print(f"🆔 Session ID: {session_id}")
print(f"🤖 LLM Model: {gemini_llm.model}")

# Step 1: Initial destination setting
# This demonstrates storing information in SQLite long-term memory using Gemini LLM

try:
    task1 = Task(
        description="User says: My destination is Pune. Store this information in long-term memory.",
        expected_output="Acknowledge that you have stored the user's destination as Pune in your long-term memory database.",
        agent=agent
    )
    
    # Create crew with:
    # - Gemini LLM for reasoning and text generation
    # - SQLite database for long-term memory storage
    # - Placeholder for memory embeddings
    crew1 = Crew(
        agents=[agent],
        tasks=[task1],
        process=Process.sequential,
        memory=True,
        long_term_memory=long_memory,
        manager_llm=gemini_llm  # Use Gemini for crew management
    )
    
    print("🚀 Starting crew execution with Gemini LLM and SQLite memory...")
    result1 = crew1.kickoff()
    print("\n✅ Result:")
    print(result1)
    
except Exception as e:
    print(f"❌ Error: {e}")
    print("\n💡 Troubleshooting:")
    print("1. Make sure you've run the API key setup cell first")
    print("2. Check that your Gemini API key is valid")
    print("3. Ensure all previous cells have been executed successfully")

for i in range(10):
    task = Task(
        description=f"User says: Let's talk about topic {i}",
        expected_output="Respond accordingly without affecting stored destination",
        agent=agent
    )
    Crew(agents=[agent], tasks=[task], process=Process.sequential, memory=True, long_term_memory=long_memory).kickoff()

# Step 3: Update destination
task2 = Task(
    description="User says: Change my destination to Delhi",
    expected_output="Update stored destination to Delhi",
    agent=agent
)
Crew(agents=[agent], tasks=[task2], process=Process.sequential, memory=True, long_term_memory=long_memory).kickoff()

task3 = Task(
    description="User says: What is my destination?",
    expected_output="Recall and respond: Your destination is Delhi",
    agent=agent
)
Crew(agents=[agent], tasks=[task3], process=Process.sequential, memory=True, long_term_memory=long_memory).kickoff()

