from crewai import Crew, Agent, Task, Process
from crewai.memory import LongTermMemory
from crewai.memory.storage.ltm_sqlite_storage import LTMSQLiteStorage
import sqlite3
import uuid

# Setup long-term memory with SQLite
shared_db = "./agent_memorydb.db"
storage = LTMSQLiteStorage(db_path=shared_db)
long_memory = LongTermMemory(storage=storage)

# Simulate session_id for different users
session_id = str(uuid.uuid4())

# Create agent
agent = Agent(
    role="Flight Booking Assistant",
    goal="Manage user's flight booking details like destination and travel date",
    backstory="You remember user's preferences and update them as per instructions.",
    verbose=True,
    memory=True,
    long_term_memory=long_memory,
    user_id=session_id  # Important for session separation
)

# Step 1: Initial destination setting
task1 = Task(
    description="User says: My destination is Pune",
    expected_output="Store user's destination as Pune",
    agent=agent
)

crew1 = Crew(
    agents=[agent],
    tasks=[task1],
    process=Process.sequential,
    memory=True,
    long_term_memory=long_memory
)
crew1.kickoff()

print('#####################')

# Step 2: Simulate 10 unrelated conversations
for i in range(10):
    task = Task(
        description=f"User says: Let's talk about topic {i}",
        expected_output="Respond accordingly without affecting stored destination",
        agent=agent
    )
    Crew(agents=[agent], tasks=[task], process=Process.sequential, memory=True, long_term_memory=long_memory).kickoff()

# Step 3: Update destination
task2 = Task(
    description="User says: Change my destination to Delhi",
    expected_output="Update stored destination to Delhi",
    agent=agent
)
Crew(agents=[agent], tasks=[task2], process=Process.sequential, memory=True, long_term_memory=long_memory).kickoff()

print('**********************')


# Step 4: Ask what is my destination (test recall)
task3 = Task(
    description="User says: What is my destination?",
    expected_output="Recall and respond: Your destination is Delhi",
    agent=agent
)
Crew(agents=[agent], tasks=[task3], process=Process.sequential, memory=True, long_term_memory=long_memory).kickoff()